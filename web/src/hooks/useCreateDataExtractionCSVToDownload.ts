import { useMutation } from '@tanstack/react-query';
import { message } from 'antd';
import { I18n } from '@aws-amplify/core';
import { DataExtractionService } from '@/infra/services/data-extraction';

interface CreateDataExtractionModelParams {
    organization_id: string;
    company_id: string;
    data_extraction_id: string;
}

const Service = DataExtractionService.getInstance();

export const useCreateDataExtractionModel = () => {
    return useMutation({
        mutationFn: async (params: CreateDataExtractionModelParams) => Service.createModel(params),
        onSuccess: () => {
            message.success(I18n.get('Data extraction model created successfully'));
        },
        onError: () => {
            message.error(I18n.get('Error creating data extraction model'));
        }
    });
};
